from flask import Blueprint, render_template, request, redirect, url_for, session
from models import Meme, Clip, db
from services.helpers import require_login
from services.audio_service import generate_audio_from_text
from services.video_service import create_clip_from_meme
import os
import uuid

clips_bp = Blueprint('clips', __name__)

@clips_bp.route('/generate_clips')
@require_login
def generate_clips():
    """Generate clips from approved memes with enhanced error handling and debugging"""
    import logging

    user_id = session['user_id']

    logging.info(f"Starting clip generation for user {user_id}")

    # Only generate clips for memes that have been approved through the workflow
    # This includes both text-approved memes (with or without audio) and audio-approved memes
    memes = Meme.query.filter_by(
        user_id=user_id,
        discarded=False,
        text_approved=True
    ).all()

    logging.info(f"Found {len(memes)} text-approved memes for clip generation")

    generated_count = 0
    failed_count = 0
    skipped_count = 0
    error_messages = []

    for meme in memes:
        try:
            # Skip if clip already exists for this meme
            existing_clip = Clip.query.filter_by(meme_id=meme.id, discarded=False).first()
            if existing_clip:
                logging.info(f"Skipping meme {meme.id} - clip already exists")
                skipped_count += 1
                continue

            logging.info(f"Processing meme {meme.id} for clip generation")

            # Generate unique filenames
            clip_filename = f"clip_{meme.id}_{uuid.uuid4().hex[:8]}.mp4"
            clip_path = os.path.join('static', 'clips', clip_filename)

            # Ensure clips directory exists
            os.makedirs(os.path.dirname(clip_path), exist_ok=True)

            audio_path = None

            # Check if meme has text and is approved for audio
            if meme.text and meme.text.strip() and meme.audio_approved:
                logging.info(f"Meme {meme.id} has text and audio approval - setting up audio")

                # Use existing audio path from session if available
                session_audio_key = f'audio_path_{meme.id}'
                if session_audio_key in session:
                    audio_path = session[session_audio_key]
                    logging.info(f"Found audio path in session: {audio_path}")

                    # Verify the file still exists
                    if not os.path.exists(audio_path):
                        logging.warning(f"Audio file not found, regenerating: {audio_path}")
                        # Generate new audio if file doesn't exist
                        audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                        audio_path = os.path.join('static', 'audio', audio_filename)
                        os.makedirs(os.path.dirname(audio_path), exist_ok=True)

                        if not generate_audio_from_text(meme.text, audio_path):
                            logging.error(f"Failed to regenerate audio for meme {meme.id}")
                            audio_path = None
                else:
                    logging.info(f"No audio in session, generating new audio for meme {meme.id}")
                    # Generate new audio
                    audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                    audio_path = os.path.join('static', 'audio', audio_filename)
                    os.makedirs(os.path.dirname(audio_path), exist_ok=True)

                    if not generate_audio_from_text(meme.text, audio_path):
                        logging.error(f"Failed to generate audio for meme {meme.id}")
                        audio_path = None
            else:
                logging.info(f"Meme {meme.id} will be image-only (no text or not audio approved)")

            # Generate clip (with or without audio)
            logging.info(f"Creating clip for meme {meme.id}: {clip_path}")

            if create_clip_from_meme(meme, audio_path, clip_path):
                # Save clip record to database
                clip_record = Clip(
                    user_id=user_id,
                    meme_id=meme.id,
                    clip_path=clip_path,
                    audio_path=audio_path,
                    text=meme.text or ""
                )
                db.session.add(clip_record)
                generated_count += 1
                logging.info(f"Successfully created clip for meme {meme.id}")
            else:
                failed_count += 1
                error_msg = f"Failed to create clip for meme {meme.id}"
                error_messages.append(error_msg)
                logging.error(error_msg)

        except Exception as e:
            failed_count += 1
            error_msg = f"Error processing meme {meme.id}: {str(e)}"
            error_messages.append(error_msg)
            logging.error(error_msg)
            continue

    # Commit database changes
    try:
        db.session.commit()
        logging.info("Database changes committed successfully")
    except Exception as e:
        logging.error(f"Error committing database changes: {e}")
        db.session.rollback()

    # Prepare user feedback message
    if generated_count > 0:
        message = f"Generated {generated_count} clips successfully!"
        if skipped_count > 0:
            message += f" Skipped {skipped_count} existing clips."
        if failed_count > 0:
            message += f" {failed_count} clips failed to generate."
    elif skipped_count > 0:
        message = f"All {skipped_count} clips already exist. No new clips generated."
    elif failed_count > 0:
        message = f"Failed to generate {failed_count} clips. Check logs for details."
    else:
        message = "No clips to generate. Make sure you have text-approved memes."

    session['flash_message'] = message
    logging.info(f"Clip generation completed: {message}")

    return redirect(url_for('clips.video_workshop'))

@clips_bp.route('/video_workshop')
@require_login
def video_workshop():
    """Video Workshop - Review generated clips organized by subreddit"""
    user_id = session['user_id']
    clips = Clip.query.filter_by(user_id=user_id, discarded=False).all()

    # Group clips by subreddit
    clips_by_subreddit = {}
    for clip in clips:
        # Get the meme to access subreddit information
        meme = Meme.query.get(clip.meme_id)
        if meme:
            subreddit = meme.subreddit
            if subreddit not in clips_by_subreddit:
                clips_by_subreddit[subreddit] = []
            clips_by_subreddit[subreddit].append(clip)

    return render_template('review_clips.html', clips_by_subreddit=clips_by_subreddit)

@clips_bp.route('/review_videos')
@require_login
def review_videos():
    """Review Videos - Show generated clips as videos organized by subreddit"""
    user_id = session['user_id']
    clips = Clip.query.filter_by(user_id=user_id, discarded=False).all()

    # Group clips by subreddit for the videos template
    videos_by_subreddit = {}
    for clip in clips:
        # Get the meme to access subreddit information
        meme = Meme.query.get(clip.meme_id)
        if meme:
            subreddit = meme.subreddit
            if subreddit not in videos_by_subreddit:
                videos_by_subreddit[subreddit] = []
            # Add video_path attribute for template compatibility
            clip.video_path = clip.clip_path
            videos_by_subreddit[subreddit].append(clip)

    return render_template('review_videos.html', videos_by_subreddit=videos_by_subreddit)

@clips_bp.route('/review_clips')
@require_login
def review_clips():
    """Legacy route - redirect to video workshop"""
    return redirect(url_for('clips.video_workshop'))

@clips_bp.route('/discard_video', methods=['POST'])
@require_login
def discard_video():
    """Discard a video (clip)"""
    user_id = session['user_id']
    video_id = request.form.get('video_id')

    clip = Clip.query.filter_by(id=video_id, user_id=user_id).first()
    if clip:
        clip.discarded = True
        db.session.commit()

    return redirect(url_for('clips.review_videos'))

@clips_bp.route('/discard_clip', methods=['POST'])
@require_login
def discard_clip():
    """Discard a clip"""
    user_id = session['user_id']
    clip_id = request.form.get('clip_id')

    clip = Clip.query.filter_by(id=clip_id, user_id=user_id).first()
    if clip:
        clip.discarded = True
        db.session.commit()

    return redirect(url_for('clips.video_workshop'))
