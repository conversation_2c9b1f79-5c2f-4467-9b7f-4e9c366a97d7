from flask import Blueprint, render_template, request, redirect, url_for, session
from models import Meme, db
from services.helpers import require_login, get_user_config, save_meme_to_db
from services.reddit_service import get_top_memes

memes_bp = Blueprint('memes', __name__)

@memes_bp.route('/fetch_memes')
@require_login
def fetch_memes():
    """Fetch memes from Reddit"""
    user_id = session['user_id']
    config = get_user_config(user_id)

    # Parse subreddits from config
    subreddits = [s.strip() for s in config.subreddits.split(',') if s.strip()]

    try:
        # Fetch memes from Reddit
        reddit_memes = get_top_memes(limit=config.max_memes, subreddits=subreddits)

        if not reddit_memes:
            session['flash_message'] = "No memes could be fetched from Reddit. Please check your internet connection and try again."
            return redirect(url_for('memes.meme_workshop'))

        # Get existing meme URLs to avoid duplicates
        existing_urls = set(meme.url for meme in Meme.query.filter_by(user_id=user_id, discarded=False).all())

        new_memes_count = 0
        duplicate_count = 0
        error_count = 0

        for meme_info in reddit_memes:
            try:
                meme_url = meme_info.get('url', '')

                if not meme_url:
                    error_count += 1
                    continue

                # Check for duplicates
                if meme_url in existing_urls:
                    duplicate_count += 1
                    continue

                # Save new meme to database with subreddit information
                save_meme_to_db(user_id, meme_url, "", meme_info.get('subreddit', 'memes'))
                existing_urls.add(meme_url)  # Add to set to prevent duplicates in this batch
                new_memes_count += 1

            except Exception:
                error_count += 1
                continue

        # Set flash message for user feedback
        if new_memes_count > 0:
            session['flash_message'] = f"Successfully fetched {new_memes_count} new memes!"
            if duplicate_count > 0:
                session['flash_message'] += f" ({duplicate_count} duplicates skipped)"
        elif duplicate_count > 0:
            session['flash_message'] = f"All {duplicate_count} memes were already in your collection."
        else:
            session['flash_message'] = "No new memes could be fetched. Please try again later."

    except Exception as e:
        session['flash_message'] = f"Error fetching memes: {str(e)}"

    return redirect(url_for('memes.meme_workshop'))

@memes_bp.route('/clear_memes', methods=['GET', 'POST'])
@require_login
def clear_memes():
    """Clear all existing memes for the user"""
    user_id = session['user_id']

    try:
        # Count memes before deletion
        meme_count = Meme.query.filter_by(user_id=user_id, discarded=False).count()

        # Clear existing memes for this user
        Meme.query.filter_by(user_id=user_id).delete()
        db.session.commit()

        # Clear audio session data
        audio_keys_to_remove = [key for key in session.keys() if key.startswith('audio_path_')]
        for key in audio_keys_to_remove:
            session.pop(key, None)

        session['flash_message'] = f"Cleared {meme_count} memes from your collection."

    except Exception as e:
        session['flash_message'] = f"Error clearing memes: {str(e)}"

    return redirect(url_for('memes.meme_workshop'))

@memes_bp.route('/meme_workshop')
@require_login
def meme_workshop():
    """Meme Workshop - Combined fetching and editing interface"""
    return render_template('meme_workshop.html')

@memes_bp.route('/review_memes')
@require_login
def review_memes():
    """Review and edit memes - organized by subreddit (only non-approved memes)"""
    user_id = session['user_id']
    # Only show memes that haven't been approved for text yet
    memes = Meme.query.filter_by(user_id=user_id, discarded=False, text_approved=False).all()

    # Group memes by subreddit
    memes_by_subreddit = {}
    for meme in memes:
        if meme.subreddit not in memes_by_subreddit:
            memes_by_subreddit[meme.subreddit] = []
        memes_by_subreddit[meme.subreddit].append(meme)

    return render_template('review_memes.html', memes_by_subreddit=memes_by_subreddit)

@memes_bp.route('/update_meme_text', methods=['POST'])
@require_login
def update_meme_text():
    """Update meme text"""
    user_id = session['user_id']
    meme_id = request.form.get('meme_id')
    new_text = request.form.get('text', '')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        meme.text = new_text
        db.session.commit()

    return redirect(url_for('memes.review_memes'))

@memes_bp.route('/discard_meme', methods=['POST'])
@require_login
def discard_meme():
    """Discard a meme"""
    user_id = session['user_id']
    meme_id = request.form.get('meme_id')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        meme.discarded = True
        db.session.commit()

    return redirect(url_for('memes.review_memes'))

@memes_bp.route('/approve_meme_text', methods=['POST'])
@require_login
def approve_meme_text():
    """Save text and approve meme for text phase in one action"""
    user_id = session['user_id']
    meme_id = request.form.get('meme_id')
    new_text = request.form.get('text', '')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        # Save text and approve in one action
        meme.text = new_text
        meme.text_approved = True
        db.session.commit()

    return redirect(url_for('memes.review_memes'))

@memes_bp.route('/clear_audio_session')
@require_login
def clear_audio_session():
    """Clear audio session data - useful after data cleanup"""
    # Clear audio session data
    audio_keys_to_remove = [key for key in session.keys() if key.startswith('audio_path_')]
    for key in audio_keys_to_remove:
        session.pop(key, None)

    session['flash_message'] = f"Cleared {len(audio_keys_to_remove)} audio session entries."
    return redirect(url_for('memes.audio_workshop'))

@memes_bp.route('/audio_workshop')
@require_login
def audio_workshop():
    """Audio Workshop - Show memes approved for audio generation"""
    user_id = session['user_id']

    # Get memes that are approved for text but not yet processed for audio
    # Only include memes that have text content for audio generation
    memes = Meme.query.filter_by(
        user_id=user_id,
        discarded=False,
        text_approved=True,
        audio_approved=False
    ).filter((Meme.text != None) & (Meme.text != '')).all()

    # Group by subreddit
    memes_by_subreddit = {}
    for meme in memes:
        if meme.subreddit not in memes_by_subreddit:
            memes_by_subreddit[meme.subreddit] = []
        memes_by_subreddit[meme.subreddit].append(meme)

    return render_template('workflow_audio.html', memes_by_subreddit=memes_by_subreddit)

@memes_bp.route('/workflow_audio')
@require_login
def workflow_audio():
    """Legacy route - redirect to audio workshop"""
    return redirect(url_for('memes.audio_workshop'))

@memes_bp.route('/approve_meme_audio', methods=['POST'])
@require_login
def approve_meme_audio():
    """Approve meme audio and move to video generation"""
    user_id = session['user_id']
    meme_id = request.form.get('meme_id')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        meme.audio_approved = True
        db.session.commit()

    return redirect(url_for('memes.audio_workshop'))

@memes_bp.route('/generate_audio', methods=['POST'])
@require_login
def generate_audio():
    """Generate audio for specific meme(s) - handles both individual and bulk generation"""
    from services.audio_service import generate_audio_from_text
    import uuid
    import os

    user_id = session['user_id']
    meme_id = request.form.get('meme_id')  # If provided, generate for specific meme
    generate_all = request.form.get('generate_all')  # If 'true', generate for all

    generated_count = 0
    failed_count = 0

    if meme_id:
        # Generate audio for specific meme
        meme = Meme.query.filter_by(id=meme_id, user_id=user_id, text_approved=True).first()
        if meme and meme.text and meme.text.strip():
            try:
                # Generate unique audio filename
                audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                audio_path = os.path.join('static', 'audio', audio_filename)

                # Generate audio
                if generate_audio_from_text(meme.text, audio_path):
                    # Store audio path in session for review
                    session[f'audio_path_{meme.id}'] = audio_path
                    generated_count = 1
                    session['flash_message'] = f"Audio generated successfully for meme {meme.id}!"
                else:
                    failed_count = 1
                    session['flash_message'] = f"Failed to generate audio for meme {meme.id}."
            except Exception as e:
                failed_count = 1
                session['flash_message'] = f"Error generating audio: {str(e)}"
        else:
            session['flash_message'] = "Invalid meme or no text content."

    elif generate_all == 'true':
        # Generate audio for all approved memes with text
        memes = Meme.query.filter_by(
            user_id=user_id,
            discarded=False,
            text_approved=True,
            audio_approved=False
        ).filter((Meme.text != None) & (Meme.text != '')).all()

        for meme in memes:
            try:
                # Skip if audio already exists in session
                if f'audio_path_{meme.id}' in session:
                    continue

                # Generate unique audio filename
                audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                audio_path = os.path.join('static', 'audio', audio_filename)

                # Generate audio
                if generate_audio_from_text(meme.text, audio_path):
                    # Store audio path in session for review
                    session[f'audio_path_{meme.id}'] = audio_path
                    generated_count += 1
                else:
                    failed_count += 1
            except Exception:
                failed_count += 1
                continue

        # Set appropriate flash message
        if generated_count > 0 and failed_count == 0:
            session['flash_message'] = f"Generated audio for {generated_count} memes! Review and approve them below."
        elif generated_count > 0 and failed_count > 0:
            session['flash_message'] = f"Generated audio for {generated_count} memes. {failed_count} failed to generate."
        elif generated_count == 0 and failed_count > 0:
            session['flash_message'] = f"Failed to generate audio for {failed_count} memes."
        else:
            session['flash_message'] = "No audio files could be generated."
    else:
        session['flash_message'] = "Invalid request parameters."

    return redirect(url_for('memes.workflow_audio'))