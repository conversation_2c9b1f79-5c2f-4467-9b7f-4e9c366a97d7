<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Memes</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/memes.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/meme_workshop">🎭 Meme Workshop</a>
            <a href="/audio_workshop">🎵 Audio Workshop</a>
            <a href="/video_workshop">📹 Video Workshop</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <h1>📝 Text Review Phase</h1>
        <p>Add optional text for narration and approve memes to proceed to the next stage.</p>

        {% if memes_by_subreddit %}
            {% for subreddit, memes in memes_by_subreddit.items() %}
            <div class="subreddit-section">
                <h2>📂 r/{{ subreddit }} ({{ memes|length }} memes)</h2>

                {% for meme in memes %}
                <div class="meme-editor">
                    <div class="meme-image">
                        <img src="{{ meme.url }}" alt="Meme Image"
                             onclick="openImageModal(this.src)"
                             style="max-width: 400px; max-height: 300px; object-fit: contain; cursor: pointer;">
                    </div>
                    <div class="meme-text-editor">
                        <div class="meme-status">
                            {% if meme.text_approved %}
                                <span class="status-approved">✅ Approved for next stage</span>
                            {% else %}
                                <span class="status-pending">⏳ Pending approval</span>
                            {% endif %}
                        </div>

                        <div class="workflow-actions">
                            {% if not meme.text_approved %}
                            <form method="POST" action="/approve_meme_text" style="display: inline;">
                                <input type="hidden" name="meme_id" value="{{ meme.id }}">
                                <div class="meme-text-editor-section" style="margin-bottom: 1rem;">
                                    <label for="text_{{ meme.id }}">Meme Text (Optional):</label>
                                    <textarea id="text_{{ meme.id }}" name="text" rows="4" cols="50"
                                              placeholder="Add text for narration (leave blank for image-only video)">{{ meme.text or '' }}</textarea>
                                </div>
                                <button type="submit" class="btn btn-success">✅ Approve & Continue</button>
                            </form>
                            {% endif %}

                            <form method="POST" action="/discard_meme" style="display: inline;">
                                <input type="hidden" name="meme_id" value="{{ meme.id }}">
                                <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('Are you sure you want to discard this meme?')">🗑️ Discard</button>
                            </form>
                        </div>
                    </div>
                </div>
                <hr>
                {% endfor %}
            </div>
            {% endfor %}

            <div class="actions">
                <a href="/audio_workshop" class="btn btn-secondary">🎵 Continue to Audio Workshop</a>
                <a href="/video_workshop" class="btn btn-secondary">🎬 Continue to Video Workshop</a>
                <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
            </div>
        {% else %}
            <p>No memes available for editing. <a href="/meme_workshop" class="btn btn-primary">🎭 Go to Meme Workshop</a> to fetch some memes first!</p>
        {% endif %}
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>

    <script>
        function openImageModal(src) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.classList.add('show');
            modalImg.src = src;
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
</body>
</html>
