import os
import logging
import requests
import subprocess
import shutil

def check_ffmpeg_available():
    """Check if FFmpeg is available on the system"""
    try:
        result = subprocess.run(['ffmpeg', '-version'],
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        return False

def get_audio_duration(audio_path):
    """Get audio duration in seconds using FFmpeg"""
    try:
        if not audio_path or not os.path.exists(audio_path):
            return 3.0  # Default duration for image-only videos

        # Use FFprobe (part of FFmpeg) to get exact duration
        cmd = [
            'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
            '-of', 'csv=p=0', audio_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

        if result.returncode == 0 and result.stdout.strip():
            duration = float(result.stdout.strip())
            # Ensure reasonable duration bounds
            duration = max(3.0, min(15.0, duration))
            logging.info(f"Audio duration: {duration:.2f} seconds for {audio_path}")
            return duration
        else:
            logging.warning(f"Could not get audio duration for {audio_path}, using default")
            return 3.0

    except Exception as e:
        logging.error(f"Error getting audio duration: {e}")
        return 3.0

def download_image_from_url(url, local_path):
    """Download image from URL to local path"""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        with open(local_path, 'wb') as f:
            f.write(response.content)

        logging.info(f"Downloaded image from {url} to {local_path}")
        return True
    except Exception as e:
        logging.error(f"Error downloading image from {url}: {e}")
        return False

def create_clip_with_ffmpeg(meme, audio_path, output_path):
    """Create clip using FFmpeg with enhanced error handling"""
    try:
        logging.info(f"Starting FFmpeg clip creation for meme {meme.id}")

        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Handle image source - try local first, then download from URL
        image_source = None

        if meme.image_path and os.path.exists(meme.image_path):
            image_source = meme.image_path
            logging.info(f"Using local image: {image_source}")
        elif meme.url:
            # Try to download image from URL
            temp_image_path = os.path.join('static', 'temp', f"temp_{meme.id}.jpg")
            os.makedirs(os.path.dirname(temp_image_path), exist_ok=True)

            if download_image_from_url(meme.url, temp_image_path):
                image_source = temp_image_path
                logging.info(f"Downloaded and using image: {image_source}")
            else:
                logging.error(f"Failed to download image from {meme.url}")
                return False
        else:
            logging.error(f"No image source available for meme {meme.id}")
            return False

        # Determine clip duration
        clip_duration = get_audio_duration(audio_path) if audio_path else 3.0
        logging.info(f"Clip duration: {clip_duration} seconds")

        # Build FFmpeg command
        cmd = ['ffmpeg', '-y']  # -y to overwrite output file

        # Input image (loop it for the duration)
        cmd.extend(['-loop', '1', '-i', image_source])

        # Add audio if available
        if audio_path and os.path.exists(audio_path):
            cmd.extend(['-i', audio_path])
            # Use audio duration, but limit video to audio length
            cmd.extend(['-shortest'])
        else:
            # No audio, set video duration
            cmd.extend(['-t', str(clip_duration)])

        # Video encoding settings
        cmd.extend([
            '-c:v', 'libx264',  # H.264 codec
            '-pix_fmt', 'yuv420p',  # Pixel format for compatibility
            '-r', '30',  # Frame rate
            '-vf', 'scale=1280:720:force_original_aspect_ratio=decrease,pad=1280:720:(ow-iw)/2:(oh-ih)/2',  # Scale to 720p with padding
        ])

        # Audio encoding settings (if audio present)
        if audio_path and os.path.exists(audio_path):
            cmd.extend(['-c:a', 'aac', '-b:a', '128k'])

        # Output file
        cmd.append(output_path)

        logging.info(f"FFmpeg command: {' '.join(cmd)}")

        # Run FFmpeg
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            # Clean up temporary image if we downloaded it
            if image_source != meme.image_path and os.path.exists(image_source):
                try:
                    os.remove(image_source)
                    logging.info(f"Cleaned up temporary image: {image_source}")
                except:
                    pass

            # Verify clip file was created
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
                logging.info(f"Clip created successfully: {output_path} ({os.path.getsize(output_path)} bytes)")
                return True
            else:
                logging.error(f"Clip file was not created or is too small: {output_path}")
                return False
        else:
            logging.error(f"FFmpeg failed with return code {result.returncode}")
            logging.error(f"FFmpeg stderr: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        logging.error("FFmpeg process timed out")
        return False
    except Exception as e:
        logging.error(f"Error creating clip with FFmpeg: {e}")
        return False

def create_clip_from_meme(meme, audio_path, output_path):
    """Create clip from meme image and audio using FFmpeg"""
    logging.info(f"Creating clip for meme {meme.id}: {output_path}")

    # Check if FFmpeg is available
    if not check_ffmpeg_available():
        logging.error("FFmpeg is not available on this system")
        # Create placeholder file to indicate the issue
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            placeholder_path = output_path.replace('.mp4', '_placeholder.txt')
            with open(placeholder_path, 'w') as f:
                f.write(f"Clip placeholder for meme ID: {meme.id}\n")
                f.write(f"Meme URL: {meme.url}\n")
                f.write(f"Audio file: {audio_path}\n")
                f.write(f"Text: {meme.text}\n")
                f.write(f"Image path: {meme.image_path}\n")
                f.write("FFmpeg is required to generate video clips.\n")
                f.write("Please install FFmpeg: https://ffmpeg.org/download.html\n")
            logging.info(f"Created placeholder file: {placeholder_path}")
        except Exception as e:
            logging.error(f"Error creating placeholder: {e}")
        return False

    # Use FFmpeg for clip generation
    success = create_clip_with_ffmpeg(meme, audio_path, output_path)
    if success:
        logging.info(f"Successfully created clip using FFmpeg: {output_path}")
        return True
    else:
        logging.error(f"Failed to create clip using FFmpeg for meme {meme.id}")
        return False
